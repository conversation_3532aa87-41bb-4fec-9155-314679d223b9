﻿using DG.Web.Framework;

using DH;
using DH.Entity;

using NewLife;
using NewLife.Log;
using NewLife.Threading;
using NewLife.Web;

using Pek.Configs;

using Stardust;

namespace AIKeFu;

/// <summary>
/// 应用程序
/// </summary>
public class Program
{
    /// <summary>
    /// 应用程序入口点
    /// </summary>
    /// <param name="args">入口点参数</param>
    public static void Main(string[] args)
    {
        foreach (var item in args)
        {
            var arr = item.SplitAsDictionary();
            foreach (var kv in arr)
            {
                if (!kv.Value.IsNullOrWhiteSpace())
                {
                    Environment.SetEnvironmentVariable(kv.Key, kv.Value);
                }
            }
        }

        if (DHSetting.Current.Debug)
        {
            XTrace.UseConsole(); // 日志输出到控制台，并拦截全局异常
        }

        XCode.Cache.CacheBase.Debug = false;  // 用于调试缓存问题

        Run(args);
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>
         CubeService.CreateHostBuilder<Startup>(args);

    private static void CheckRegion()
    {
        var wc = new WebClientX()
        {
            Log = XTrace.Log
        };

        var content = "Data".AsDirectory();

        wc.DownloadLinkAndExtract("http://x.chuangchu.net/", "3-Regions-20200622.zip", content.FullName, true);
        wc.DownloadLinkAndExtract("http://x.chuangchu.net/", "ip2region-20230908.zip", content.FullName, true);
        wc.DownloadLinkAndExtract("http://x.chuangchu.net/", "GeoLite2-ASN-20210320.zip", content.FullName, true);
        wc.DownloadLinkAndExtract("http://x.chuangchu.net/", "GeoLite2-City-20210320.zip", content.FullName, true);
    }

    public static void Run(string[] args)
    {
        var set = DG.Setting.Current;

        DHSetting.Current.CurrentVersion = "1.0.0";
        //DHSetting.Current.UniversalCaptchaEndTime = DateTime.Now.AddDays(7);
        DHSetting.Current.TokenCookie = true;
        DHSetting.Current.Save();

        PekSysSetting.Current.ExcludeUrl = "SendMail/Send,/otaserver/,/Common/GetProfilePicture";
        PekSysSetting.Current.Save();

        RedisSetting.Current.IsUseRedisCache = true;
        RedisSetting.Current.Save();

        if (!set.HasInstalled)
        {
            set.HasInstalled = true;
            set.IsOnlyManager = false;
            DHSetting.Current.CurDomainUrl = "http://localhost:9330";
            set.Urls = "http://*:9330;https://*:9331"; // 如果有使用ListenAnyIP监听时建议此处设为空。
            set.IsAlertOrCheckCode = 2;

            set.IsAllowSignalR = true;
            set.CORSUrl = "https://localhost:9330,http://localhost:9331";
            set.CorrelationClientName = "AIKeFu";

            set.CaptChaUrl = "/CaptCha/GetCheckCode";
            set.LogoutAll = true;
            set.LoginUrl = "~/Login";
            set.AllowLogin = false;
            set.UserCenterUrl = "~/";

            set.Save();

            if (!DHSetting.Current.IsInstalled)
            {
                DHSetting.Current.SessionTimeout = 7200;
                DHSetting.Current.AdminArea = "Biz";
                DHSetting.Current.Save();

                RedisSetting.Current.RedisEnabled = true;
                RedisSetting.Current.CacheKeyPrefix = set.CorrelationClientName;
                RedisSetting.Current.RedisDatabaseId = 3;
                RedisSetting.Current.Save();

                var siteInfo = SiteInfo.FindDefault();
                if (siteInfo.Url.IsNullOrWhiteSpace())
                {
                    siteInfo.Url = $"{DHSetting.Current.CurDomainUrl}/";
                    siteInfo.Save();
                }
            }
            //else
            //{
            //    UtilSetting.Current.RedisConnectionString = DHUtilSetting.Current.RedisConnectionString;
            //    UtilSetting.Current.RedisPassWord = DHUtilSetting.Current.RedisPassWord;
            //    UtilSetting.Current.RedisEnabled = DHUtilSetting.Current.RedisEnabled;
            //    UtilSetting.Current.CacheKeyPrefix = set.CorrelationClientName;
            //    UtilSetting.Current.RedisDatabaseId = DHUtilSetting.Current.RedisDatabaseId;
            //    UtilSetting.Current.Save();
            //}

            ThreadPoolX.QueueUserWorkItem(CheckRegion);
        }

        //RedisSetting.Current.RedisEnabled = DHUtilSetting.Current.RedisEnabled;
        //RedisSetting.Current.CacheKeyPrefix = DHUtilSetting.Current.CacheKeyPrefix;
        //RedisSetting.Current.Save();

        if (StarSetting.Current.Server.IsNullOrWhiteSpace() || StarSetting.Current.AppKey.IsNullOrWhiteSpace())
        {
            StarSetting.Current.Server = "http://star.chuangchu.net:6600";

            StarSetting.Current.AppKey = set.CorrelationClientName;
            StarSetting.Current.Save();
        }

        //DAL.AddConnStr("","") // 用于添加自定义链接

        //// 用于重启系统
        //var app = ApplicationManager.Load();
        //do
        //{
        //    app.Start(CreateHostBuilder(args).Build());
        //} while (app.Restarting);

        CreateHostBuilder(args).Build().Run();
    }

}
