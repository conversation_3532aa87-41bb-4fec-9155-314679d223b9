﻿using NewLife.Log;

using System.Security.Cryptography;
using System.Text.RegularExpressions;

XTrace.UseConsole();
Console.WriteLine("Hello, World!");

//using (var rsa = RSA.Create())
//{
//    rsa.ImportRSAPrivateKey(Convert.FromBase64String("MIIEpAIBAAKCAQEAmr4d5j1KVeP3pEifnePhJ5mZApOxOECr6hm0QrZjRj9fi5wbC1lBoNUqZC7+NxdlwykqBMY77hjZR2JmXLvDJFGnWC9fFxaTVYNS6pEtb/UvOcMubfY3qA7E8PWYkfnO5H0HuDshohzAeSU+Q7kP2mvhlHWFHFqdxku8o99Y3SChzrV/6zNwXivzuVbqst/k3Evwgrmr6Gpjdpo/MmpV0MvD0TbXwzf4yEi4hRla8CjAyp5ITdDqzXGBnj/caSCZi/NHoksLKrLHt2LPcpLb3DZ313C9Py3o0iww66qx/s3hK9BoId8b5nVwvnlsNghzN8sFZWoNrNn7r6PVE85qkwIDAQABAoIBAFwEL41o5to6TFlRojDmgE3nBVjF8H1N36y+ijWddV0hdDR1qJNQCTmVaLbYRu0FqcqqoMTkCGEmP5Y2Sj5cnMbtE0DaMoZtM0GHbPf1Snqn5uRHT0WuQxxZY/0RXh931/KA8uU3rGFVDZM8dVEaK/YrbqG0XGom7GktQkiEGLisrNEtia8t4xiZMJzR1sQczheIfkbwsmTtyKrUM2zco2VJ+xwO3F3Cyp9iZQEhsRGrq4J622mnO5j77zVIomRJIWYp26xroxwO16+KfBbznvZDOlta3gD98bBhkSqaIq3n16bJ6/F1OxuTv92KVuJilh2kWLS+kg6IQzjS3E8grkkCgYEA8T7ZiMZ3ZxfOzgTQajgVKEDnr1w1+41X8d85UGqHPmaOA2oxH/kJe8Fk4peLDVTR5qdaJjfRgkosO7fEFBvPzK78694p4P8X2oHvMkvJ06eyNYB+yRrpDIkHeIsVobvOm66Seij4ftUMKY0BoPFUb99YvIqoQ86Kv8LjzLek53cCgYEApDTnGBvIp0g25Qo9u70ijLQnIZ3QX4AjwXJMLfy4lPSU5KSt1dGwXH8Cp6BTh3NvM8A6zKBqRwd7Kevsdl/HEgNPdhYTXBwaSa2LctZ5iORyLV4alkqee7gvFR374SRgLGlIFJa+8j/UIgYk5a1xTT0QWGO7UBXUF+0atBmFFMUCgYEAw7X4JlTT4yfWVx5isflDeL+G9aV6CdQ153vvdESn6UCrryuEJOsU/Xf8TzMf1LXd/x//K4gXCHsB/YRspt+VKflAKF24Xda01Eia85O2bWXbVjojISUasVetXuZ+qxHAYF40aLQ6PDK8Ri0liBqgJI3t4C8gqLmzqDQEOD03o6sCgYAxnXJ+DFcShHVhuzhv83PvA7gXJjIByRwEpV5zPrse9s/OThvgUV1ydUX+FClo8e4Vuu2JSqcSEyaZwhlOox3pSWrHCWCMIkmnHXLXHgrW2//LyXVLF/zO8RX40Xh7zJvqj10iK3JCjBqytRyM8V0cs2iu7GN6jT3qhM/evx9A6QKBgQCRA0dGxOWmdD4lGPmF8fuRCgPS29lKTbVIftjw00/9FcAUOzKvxOqREJMmgMfEYsLP5NPzI2n0JysLCTf1PFZSszUK92711shCM8Ooifym/XGT+ewAvPaFXE9uxRn38a57hbKOoguuwlUnGHCbNJybD724c0I8SY0cniclIon7WQ=="), out var _);
//}

var Content = "event:message\r\ndata:{\"err_no\":0,\"msg\":\"success\",\"data\":{\"id\":\"\",\"object\":\"\",\"sentence_id\":0,\"created\":0,\"result\":\"**【参考您导入的语料进行作答】**\\n\\n\",\"need_clear_history\":false,\"is_end\":false,\"usage\":{\"prompt_tokens\":0,\"completion_tokens\":0,\"total_tokens\":0},\"hits\":[{\"Q\":\"2410B参数介绍\",\"A\":\"距离门：每个距离门分辨率为0.75m或0.2m，可设置距离门个数范围为1～8。实际探测距离=距离门分辨率X距离门个数。最远探测距离为0.75 X 8 =6m。\\r\\n无人持续时间：雷达在输出从有人到无人的结果中，会持续一段时间上报有人，若在此时间段雷达测试范围内持续无人，雷达上报无人；若在此时间段雷达检测到有人，则重刷新此时间，单位秒。\\r\\n灵敏度： 灵敏度值可设置范围0～100。每个距离门可独立设置灵敏度，即可对不同距离范围内的探测进行精准调节，局部精准探测或对特定区域干扰源的过滤。\",\"score\":0.7587784262954513},{\"Q\":\"2410Bapp搜不到蓝牙，连接不上。\",\"A\":\"测模块工作电流是否是正常的70-80mA，如果供电电压电流在模块正常工作状态，用串口助手发串口指令让模块恢复出厂设置重试。\",\"score\":0.7152821563526678},{\"Q\":\"2410B单模块接线图\",\"A\":\"http://kf.hlktech.com/Uploads/Files/202308/n3zzoklarfunx.png\",\"score\":0.6894096436366377}]}}\r\n\r\nevent:message\r\ndata:{\"err_no\":0,\"msg\":\"success\",\"data\":{\"id\":\"\",\"object\":\"\",\"sentence_id\":0,\"created\":0,\"result\":\"距离门：每个距离门分辨率为0.75m或0.2m，可设置距离门个数范围为1～8。实际探测距离=距离门分辨率X距离门个数。最远探测距离为0.75 X 8 =6m。\\r\\n无人持续时间：雷达在输出从有人到无人的结果中，会持续一段时间上报有人，若在此时间段雷达测试范围内持续无人，雷达上报无人；若在此时间段雷达检测到有人，则重刷新此时间，单位秒。\\r\\n灵敏度： 灵敏度值可设置范围0～100。每个距离门可独立设置灵敏度，即可对不同距离范围内的探测进行精准调节，局部精准探测或对特定区域干扰源的过滤。\",\"need_clear_history\":false,\"is_end\":true,\"usage\":{\"prompt_tokens\":0,\"completion_tokens\":0,\"total_tokens\":0}}}";

var sp = Content.Split("\r\n\r\n");

foreach(var item in sp)
{
    var t = item.Replace("event:message\r\ndata:", "");
    XTrace.WriteLine($"拿到的数据：{t}");
}

XTrace.WriteLine($"获取数据：{sp.Length}");

string pattern = @"data:\{.*?\}";
MatchCollection matches = Regex.Matches(Content, pattern);

foreach (Match match in matches)
{
    string jsonData = match.Value;
    Console.WriteLine(jsonData);
}