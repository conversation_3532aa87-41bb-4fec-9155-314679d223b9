﻿using NewLife.Log;
using NewLife.Serialization;

using System.Text;

namespace ALiBaiLianDemo;

internal class Program {
    static async Task Main(string[] args)
    {
        XTrace.UseConsole();

        var apiKey = "sk-aac8cd9d480d4354b512fdfefed4c275";
        var appId = "1791b53956c54458aa38433f44ee48ef";

        string url = $"https://dashscope.aliyuncs.com/api/v1/apps/{appId}/completion";

        using (HttpClient client = new HttpClient())
        {
            XTrace.WriteLine($"开始");
            client.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");

            String[]? ImageList = [];
            var pluginCode = "quark_search";

            string promptText = "请介绍一下B20"; // 你可以将这个值改为动态获取
            string jsonContent = $@"{{
                ""input"": {{
                    ""prompt"": ""{promptText}"",
                    ""image_list"": ""{ImageList?.ToJson()}"",
                    ""biz_params"": {{
                        ""user_defined_params"": {{
                            ""{pluginCode}"": {{
                                ""query"": ""{promptText}""
                            }}
                        }}
                    }}
                }},
                ""parameters"": {{""has_thoughts"": true}},
                ""debug"": {{}}
            }}";

            HttpContent content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            try
            {
                HttpResponseMessage response = await client.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    string responseBody = await response.Content.ReadAsStringAsync();
                    XTrace.WriteLine("Request successful:");
                    XTrace.WriteLine(responseBody);
                }
                else
                {
                    XTrace.WriteLine($"Request failed with status code: {response.StatusCode}");
                    string responseBody = await response.Content.ReadAsStringAsync();
                    XTrace.WriteLine(responseBody);
                }
            }
            catch (Exception ex)
            {
                XTrace.WriteLine($"Error calling DashScope: {ex.Message}");
            }
        }

        //string url = $"https://dashscope.aliyuncs.com/api/v1/apps/{appId}/completion";

        //using (HttpClient client = new HttpClient())
        //{
        //    client.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");
        //    client.DefaultRequestHeaders.Add("X-DashScope-SSE", "enable");

        //    string jsonContent = @"{
        //        ""input"": {
        //            ""prompt"": ""我想知道RL05的工作温度""
        //        },
        //        ""parameters"": {""incremental_output"": true},
        //        ""debug"": {}
        //    }";

        //    HttpContent content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

        //    XTrace.WriteLine(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff"));
        //    try
        //    {
        //        var request = new HttpRequestMessage(HttpMethod.Post, url);
        //        request.Content = content;

        //        HttpResponseMessage response = await client.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);


        //        if (response.IsSuccessStatusCode)
        //        {
        //            XTrace.WriteLine("Request successful:");
        //            using (var stream = await response.Content.ReadAsStreamAsync())
        //            using (var reader = new StreamReader(stream))
        //            {
        //                string? line; // 声明为可空字符串
        //                while ((line = await reader.ReadLineAsync()) != null)
        //                {
        //                    if (line.StartsWith("data:"))
        //                    {
        //                        string data = line.Substring(5).Trim();
        //                        XTrace.WriteLine(data);
        //                    }
        //                }
        //            }
        //        }
        //        else
        //        {
        //            XTrace.WriteLine($"Request failed with status code: {response.StatusCode}");
        //            string responseBody = await response.Content.ReadAsStringAsync();
        //            XTrace.WriteLine(responseBody);
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        XTrace.WriteLine($"Error calling DashScope: {ex.Message}");
        //    }
        //}

        Console.ReadKey();
    }
}
