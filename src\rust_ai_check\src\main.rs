use reqwest::Error;
use std::{thread, time::Duration};
use sysinfo::System;

#[tokio::main]
async fn main() -> Result<(), Error> {
    loop {
        println!("This line is printed every 10 seconds");

        // Create and refresh the System object in each iteration
        let mut system = System::new_all();
        system.refresh_processes();

        // Check if the process with name "ChatBot.exe" exists
        let process_exists = system
            .processes()
            .iter()
            .any(|(_, process)| process.name() == "ChatBot.exe");

        // Do something with the process_exists variable

        println!("Process exists: {}", process_exists);

        if process_exists == false {
            // 创建reqwest的客户端实例
            let client = reqwest::Client::new();

            // 发起异步POST请求
            let resp = client
                .post("https://kf.hlktech.com/api/v1/http2/SendRobot")
                .header("Content-Length", "0")
                .body("")
                .send()
                .await?
                .text()
                .await?;

            println!("resp: {}", resp);
        }

        // 暂停当前线程10秒
        thread::sleep(Duration::from_secs(10));
    }
}
