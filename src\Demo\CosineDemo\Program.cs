﻿using JiebaNet.Segmenter;

namespace CosineDemo;

internal class Program {
    static void Main(string[] args)
    {
        string text1 = "LD2420和2410C有什么区别？";
        string text2 = "2410C和LD2420有什么区别？";

        var vector1 = TextToBagOfWords(text1);
        var vector2 = TextToBagOfWords(text2);

        double similarity = CalculateCosineSimilarity(vector1, vector2);
        Console.WriteLine("文本的余弦相似度为: " + similarity);

        text1 = "我想了解一下LD2410和LD2420这两款产品的介绍资料";
        text2 = "2410C和LD2420有什么区别？";

        vector1 = TextToBagOfWords(text1);
        vector2 = TextToBagOfWords(text2);

        similarity = CalculateCosineSimilarity(vector1, vector2);
        Console.WriteLine("文本的余弦相似度为: " + similarity);
    }

    // 将文本字符串分词并转换为词袋向量
    public static Dictionary<string, int> TextToBagOfWords(string text)
    {
        var segmenter = new JiebaSegmenter();
        IEnumerable<string> words = segmenter.Cut(text); // 中文分词

        Dictionary<string, int> bagOfWords = new Dictionary<string, int>();

        foreach (string word in words)
        {
            if (bagOfWords.ContainsKey(word))
            {
                bagOfWords[word]++;
            }
            else
            {
                bagOfWords[word] = 1;
            }
        }

        return bagOfWords;
    }

    // 计算两个词袋向量之间的余弦相似度
    public static double CalculateCosineSimilarity(Dictionary<string, int> vector1, Dictionary<string, int> vector2)
    {
        var commonWords = vector1.Keys.Intersect(vector2.Keys).ToList();
        double dotProduct = commonWords.Sum(word => vector1[word] * vector2[word]);

        double magnitude1 = Math.Sqrt(vector1.Values.Sum(value => value * value));
        double magnitude2 = Math.Sqrt(vector2.Values.Sum(value => value * value));

        return dotProduct / (magnitude1 * magnitude2);
    }
}
