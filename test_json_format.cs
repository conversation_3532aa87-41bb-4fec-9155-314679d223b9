using System.Text.Json;

// 测试新的 Dictionary 方式生成的 JSON 格式
var testMessage = @"关于HLK-V21，1.模块串口的数据传输的速率是多少，手机 MCU这边是3MBPS
2，手机充电接口的5V 700MA电流，  语音模块是否可以共用充电5V电压  选则充电器为5V 2A是否满足模块满载喇叭工作需求   你们一般适配的什么供电
3.如果用两个硅麦 是否单端方式 ，即可满足语音模块的效果，有什么需要注意的，
4. 是否除了喇叭电流需要大点，    其他外围电阻都用0201封装的过电流就可满足了";

var sessionId = "7363508459080282112";
var pluginCode = "quark_search";

// 使用 Dictionary 确保字段名完全一致
var requestData = new Dictionary<string, object>
{
    ["input"] = new Dictionary<string, object>
    {
        ["prompt"] = testMessage,
        ["image_list"] = "[]",
        ["biz_params"] = new Dictionary<string, object>
        {
            ["user_defined_params"] = new Dictionary<string, object>
            {
                [pluginCode] = new Dictionary<string, object>
                {
                    ["query"] = testMessage
                }
            }
        }
    },
    ["parameters"] = new Dictionary<string, object>(),
    ["debug"] = new Dictionary<string, object>()
};

// 添加 session_id
((Dictionary<string, object>)requestData["input"])["session_id"] = sessionId;

var jsonContent = JsonSerializer.Serialize(requestData, new JsonSerializerOptions
{
    WriteIndented = true  // 为了测试，使用缩进格式
});

Console.WriteLine("生成的 JSON 格式:");
Console.WriteLine(jsonContent);

// 验证 JSON 是否有效
try
{
    var parsed = JsonSerializer.Deserialize<object>(jsonContent);
    Console.WriteLine("\n✅ JSON 格式有效！");
    
    // 检查字段名是否正确
    var dict = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonContent);
    var input = JsonSerializer.Deserialize<Dictionary<string, object>>(dict["input"].ToString());
    
    Console.WriteLine($"✅ 包含 session_id: {input.ContainsKey("session_id")}");
    Console.WriteLine($"✅ 包含 image_list: {input.ContainsKey("image_list")}");
    Console.WriteLine($"✅ 包含 biz_params: {input.ContainsKey("biz_params")}");
}
catch (JsonException ex)
{
    Console.WriteLine($"\n❌ JSON 格式无效: {ex.Message}");
}
