{"version": 3, "sources": ["ui/default/skin.shadowdom.css"], "names": [], "mappings": ";;;;;;AAMA,gCACE,SAAU,OAEZ,gBACE,OAAQ,EACR,OAAQ,KACR,KAAM,EACN,OAAQ,EACR,SAAU,OACV,oBAAqB,KACjB,oBAAqB,KACzB,QAAS,EACT,SAAU,MACV,IAAK,EACL,aAAc,WACd,MAAO,KAET,8DACE,QAAS,KAEX,gCACE,QAAS,KAEX,+BACE,QAAS,KAEX,qCACA,qCACE,QAAS", "file": "skin.shadowdom.min.css", "sourcesContent": ["/**\n * Copyright (c) Tiny Technologies, Inc. All rights reserved.\n * Licensed under the LGPL or a commercial license.\n * For LGPL see License.txt in the project root for license information.\n * For commercial licenses see https://www.tiny.cloud/\n */\nbody.tox-dialog__disable-scroll {\n  overflow: hidden;\n}\n.tox-fullscreen {\n  border: 0;\n  height: 100%;\n  left: 0;\n  margin: 0;\n  overflow: hidden;\n  -ms-scroll-chaining: none;\n      overscroll-behavior: none;\n  padding: 0;\n  position: fixed;\n  top: 0;\n  touch-action: pinch-zoom;\n  width: 100%;\n}\n.tox.tox-tinymce.tox-fullscreen .tox-statusbar__resize-handle {\n  display: none;\n}\n.tox.tox-tinymce.tox-fullscreen {\n  z-index: 1200;\n}\n.tox-shadowhost.tox-fullscreen {\n  z-index: 1200;\n}\n.tox-fullscreen .tox.tox-tinymce-aux,\n.tox-fullscreen ~ .tox.tox-tinymce-aux {\n  z-index: 1201;\n}\n"]}