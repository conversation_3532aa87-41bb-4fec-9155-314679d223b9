#![windows_subsystem = "windows"] // Windows平台运行编译好的gui程序时就不会弹出控制台框

use std::os::windows::process::CommandExt;
use std::process::{exit, Command, Stdio};

use winapi::um::winbase::CREATE_NEW_PROCESS_GROUP;
use winapi::um::winbase::DETACHED_PROCESS;

use sysinfo::System;
use winreg::{enums::HKEY_CURRENT_USER, RegKey};

fn main() {
    // Create and refresh the System object in each iteration
    let mut system = System::new_all();
    system.refresh_processes();

    // Create a vector to store the pids
    let mut pids = Vec::new();

    // Iterate over the processes and store the pid of "WeChat.exe" in the vector
    system
        .processes()
        .iter()
        .filter(|(_, process)| process.name() == "WeChat.exe")
        .for_each(|(_, process)| pids.push(process.pid()));

    // Print the pids
    for pid in &pids {
        println!("Process ID: {}", pid);
    }

    if pids.len() > 0 {
        println!("Successfully retrieved the PIDs of WeChat.exe");

        //let _ = Command::new("start.bat")
        let _ = Command::new("wxbot-sidecar.exe")
            .arg("-p")
            .arg(format!("{}", pids[0]))
            //.stdout(Stdio::null())
            //.stderr(Stdio::null())
            //.creation_flags(CREATE_NEW_PROCESS_GROUP | DETACHED_PROCESS)
            .arg("/c") // 执行完命令后关闭命令窗口
            //.creation_flags(DETACHED_PROCESS)
            .spawn()
            .expect("failed to execute process");

        exit(0);
    } else {
        let hkcu = RegKey::predef(HKEY_CURRENT_USER);
        let subkey = "Software\\Tencent\\WeChat";
        let key = hkcu.open_subkey(subkey).unwrap();

        let mut path = String::new();

        match key.get_value::<String, _>("InstallPath") {
            Ok(value) => {
                println!("{}", value);
                path = value;
            }
            Err(e) => {
                eprintln!("Error: {}", e);
            }
        }

        if path.is_empty() {
            println!("Path is empty");
        } else {
            println!("Path is not empty");

            let _ = Command::new(path + "\\WeChat.exe")
                .stdout(Stdio::null())
                .stderr(Stdio::null())
                .creation_flags(CREATE_NEW_PROCESS_GROUP | DETACHED_PROCESS)
                .arg("/c") // 执行完命令后关闭命令窗口
                .output()
                .expect("failed to execute process");
        }
    }

    exit(0);
}
