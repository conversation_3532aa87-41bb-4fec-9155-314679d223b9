<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<LangVersion>latest</LangVersion>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UserSecretsId>8afefab6-8771-4dce-aa69-13322d265bcf</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<DockerfileContext>..\..</DockerfileContext>
		<AssemblyTitle>AI客服系统</AssemblyTitle>
		<Description>海凌科AI客服系统</Description>
		<Authors>丁川</Authors>
		<Company>深圳市海凌科电子有限公司</Company>
		<Copyright>版权所有(C) 深圳市海凌科电子有限公司 2009-2025</Copyright>
		<Version>1.0.2023.0819</Version>
		<FileVersion>1.0.2023.0815</FileVersion>
		<AssemblyVersion>1.0.*</AssemblyVersion>
		<Deterministic>false</Deterministic>
		<OutputPath>..\..\BinWeb</OutputPath>
		<DebugType>pdbonly</DebugType>
		<Optimize>true</Optimize>
		<DefineConstants>TRACE</DefineConstants>
		<GenerateRuntimeConfigDevFile>true</GenerateRuntimeConfigDevFile>
		<SatelliteResourceLanguages>en</SatelliteResourceLanguages>
		<!--允许你指定要在生成和发布过程中为哪些语言保留附属资源程序集-->

		<!--混合打包，如启用则下面的混合打包规则也要启用-->
		<!--<RazorCompileOnBuild>true</RazorCompileOnBuild>
        <RazorCompileOnPublish>true</RazorCompileOnPublish>
        <PreserveCompilationContext>true</PreserveCompilationContext>
        <PreserveCompilationReferences>true</PreserveCompilationReferences>-->

		<!--不打包模板文件-->
		<RazorCompileOnBuild>false</RazorCompileOnBuild>
		<MvcRazorCompileOnPublish>false</MvcRazorCompileOnPublish>

		<!--将此参数设置为true以获取从NuGet缓存复制到项目输出的dll。-->
		<CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>

		<NoWarn>$(NoWarn);1591</NoWarn>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)'=='Debug'">
		<DefineConstants>$(DefineConstants);DEBUG</DefineConstants>
		<DebugType>full</DebugType>
		<DebugSymbols>true</DebugSymbols>
	</PropertyGroup>

	<ItemGroup>
		<Content Update="wwwroot\**\*.*">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<!--打包模板文件夹规则，混合编译，部分预编译，部分动态编译，此处如没有设置规则，则动态编译、混合编译无效-->
	<!--<ItemGroup>
    <MvcRazorFilesToCompile Include="Views\**\*.cshtml;EmailTemplates\**\*.cshtml" Exclude="wwwroot\themes\**\*.cshtml;" />
  </ItemGroup>-->

	<ItemGroup>
		<!-- 我们复制整个\App_Data目录。 但是我们忽略了JSON文件和数据保护密钥  -->

		<Compile Remove="Entity\Config\**" />
		<Compile Remove="Entity\Log\**" />
		<Content Remove="Entity\Config\**" />
		<Content Remove="Entity\Log\**" />
		<EmbeddedResource Remove="Entity\Config\**" />
		<EmbeddedResource Remove="Entity\Log\**" />
		<None Remove="Entity\Config\**" />
		<None Remove="Entity\Log\**" />

		<Compile Remove="Themes\**" />
		<Content Remove="Themes\**" />
		<EmbeddedResource Remove="Themes\**" />
		<None Remove="Themes\**" />

		<Content Include="Themes\**" CopyToPublishDirectory="PreserveNewest" Exclude="Themes\**\*.config;Themes\**\*.cshtml;Themes\**\*.json" />
		<None Include="Themes\**" CopyToPublishDirectory="PreserveNewest" />
	</ItemGroup>

	<ItemGroup>
	  <Content Remove="compilerconfig.json" />
	</ItemGroup>

	<ItemGroup>
		<None Remove="Entity\Build.tt" />
	</ItemGroup>

	<ItemGroup>
		<Content Include="Entity\Build.tt">
			<LastGenOutput>Build.log</LastGenOutput>
			<Generator>TextTemplatingFileGenerator</Generator>
		</Content>
	</ItemGroup>

	<ItemGroup>
      <PackageReference Include="DG.DSCube" Version="8.9.2025.8190100" />
      <PackageReference Include="DG.QRCode" Version="8.9.2025.8190100" />
      <PackageReference Include="DG.QRCoder" Version="8.9.2025.8190100" />
      <PackageReference Include="DG.Utils" Version="8.9.2025.8190100" />
      <PackageReference Include="DH.AspNetCore.ServerSentEvents" Version="4.0.2025.81900090" />
      <PackageReference Include="DH.SignalR" Version="4.12.2025.713-beta0228" />
      <PackageReference Include="DH.SLazyCaptcha" Version="4.0.2025.709-beta0819" />
      <PackageReference Include="DH.Magicodes.IE.Excel" Version="4.0.2025.81900090" />
      <PackageReference Include="DH.Npoi" Version="4.0.2025.81900090" />
      <PackageReference Include="DH.ServerSentEvent.Client" Version="4.0.2025.81900090" />
      <PackageReference Include="DH.WebHook" Version="4.0.2025.81900090" />
	  <PackageReference Include="Jering.Javascript.NodeJS" Version="7.0.0" />
	  <PackageReference Include="DH.NStardust.Extensions" Version="4.14.2025.818-beta0728" />
	  <PackageReference Include="DH.NAgent" Version="4.13.2025.818-beta0356" />
	  <PackageReference Include="DG.QiNiu.Extensions" Version="8.9.2025.5160083" />
	  <PackageReference Include="Rougamo.Fody" Version="5.0.1" />
	  <!--<PackageReference Include="DH.LettuceEncrypt" Version="3.6.2024.9030175" />-->
	  <PackageReference Include="DH.NRedis.Extensions" Version="4.14.2025.818-beta0742" />
	</ItemGroup>

	<ItemGroup>
		<!-- 此设置解决了vs2019中websdk中此更新引起的问题
    https://github.com/aspnet/websdk/commit/7e6b193ddcf1eec5c0a88a9748c626775555273e#diff-edf5a48ed0d4aa5a4289cb857bf46a04
    因此，我们恢复了标准配置行为（没有副本到输出目录）
     为了避免在发布过程中出现“ Duplicate dll”错误。
     我们还可以根据以下条件使用“ ExcludeConfigFilesFromBuildOutput” https://github.com/aspnet/AspNetCore/issues/14017 -->
		<Content Update="**\*.config;**\*.json" CopyToOutputDirectory="Never" CopyToPublishDirectory="PreserveNewest" />
	</ItemGroup>

	<ItemGroup>
		<None Update="Entity\Build.log">
			<DependentUpon>Build.tt</DependentUpon>
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
		</None>
	</ItemGroup>

	<ItemGroup>
		<Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
	</ItemGroup>

	<ItemGroup>
		<Content Update="Settings\*.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Update="web.config">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
	  <PackageReference Update="Microsoft.SourceLink.GitHub" Version="8.0.0" />
	</ItemGroup>

</Project>
