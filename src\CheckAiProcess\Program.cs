﻿using NewLife.Log;
using NewLife.Threading;

using System.Diagnostics;

namespace CheckAiProcess;

internal class Program {

    /// <summary>
    /// Mqtt定时器
    /// </summary>
    private static TimerX? _timer;

    static void Main(string[] args)
    {
        XTrace.UseConsole();

        XTrace.WriteLine($"开始执行");

        _timer = new TimerX(DoSchedule, null, 100, 10_000, "CheckAiProcess") { Async = true, Absolutely = true };

        Console.ReadKey();
    }

    private static void DoSchedule(Object? state)
    {
        bool isRunning = IsProcessRunning("ChatBot");

        XTrace.WriteLine($"进程是否存在：{isRunning}");
    }

    public static bool IsProcessRunning(string processName)
    {
        // 获取所有运行中的进程
        Process[] processes = Process.GetProcesses();

        // 遍历所有进程，检查是否存在名为"ChatBot.exe"的进程
        foreach (var process in processes)
        {
            if (process.ProcessName == processName)
            {
                return true;
            }
        }

        // 如果没有找到，则返回false
        return false;
    }
}
