package main

import (
	"fmt"
	"os/exec"
	"strings"
	"time"
)

func checkProcessExists() {
	cmd := exec.Command("tasklist")
	output, err := cmd.Output()

	if err != nil {
		fmt.Println(err.Error())
		return
	}

	if strings.Contains(string(output), "ChatBot.exe") {
		fmt.Println("The process exists.")
	} else {
		fmt.Println("The process does not exist.")
	}
}

func main() {
	ticker := time.NewTicker(10 * time.Second)

	go func() {
		for {
			select {
			case <-ticker.C:
				checkProcessExists()
			}
		}
	}()

	// Keep the program running
	select {}
}
