#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/core/aspnet:3.1-buster-slim AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/core/sdk:3.1-buster AS build
WORKDIR /src
COPY ["DGB2B2C/DGB2B2C.csproj", "DGB2B2C/"]
RUN dotnet restore "DGB2B2C/DGB2B2C.csproj"
COPY . .
WORKDIR "/src/DGB2B2C"
RUN dotnet build "DGB2B2C.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "DGB2B2C.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "DGB2B2C.dll"]